/* Friends Manager Styles */
.friends-manager {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-md);
}

.friends-content {
  background: var(--color-light);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
}

.friends-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  border-bottom: 2px solid var(--color-primary);
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
}

.friends-header h2 {
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: 700;
}

.close-friends-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--color-light);
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.close-friends-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Tabs */
.friends-tabs {
  display: flex;
  background: var(--color-gray-light);
  border-bottom: 1px solid rgba(184, 134, 11, 0.2);
}

.tab-btn {
  flex: 1;
  padding: var(--space-md) var(--space-lg);
  border: none;
  background: transparent;
  color: var(--color-gray-medium);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: rgba(184, 134, 11, 0.1);
  color: var(--color-primary);
}

.tab-btn.active {
  background: var(--color-light);
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  font-weight: 600;
}

/* Body */
.friends-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

/* Friends List */
.friends-list {
  height: 100%;
}

.loading-state {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--color-gray-medium);
  font-size: var(--text-lg);
}

.empty-state {
  text-align: center;
  padding: var(--space-2xl);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  opacity: 0.5;
}

.empty-state h3 {
  color: var(--color-dark);
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-xl);
}

.empty-state p {
  color: var(--color-gray-medium);
  margin: 0 0 var(--space-lg) 0;
}

.switch-tab-btn {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.switch-tab-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.friends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.friend-card {
  background: var(--color-light);
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: var(--transition-normal);
}

.friend-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.friend-presence {
  margin-bottom: var(--space-md);
}

.friend-actions {
  display: flex;
  gap: var(--space-sm);
}

.message-friend-btn,
.remove-friend-btn {
  flex: 1;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border: 2px solid;
}

.message-friend-btn {
  background: linear-gradient(135deg, var(--color-accent-green), var(--color-accent-emerald));
  color: var(--color-light);
  border-color: var(--color-accent-green);
}

.message-friend-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.remove-friend-btn {
  background: var(--color-light);
  color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.remove-friend-btn:hover {
  background: var(--color-secondary);
  color: var(--color-light);
}

/* Friend Requests */
.friend-requests {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.requests-section h3 {
  color: var(--color-primary);
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--text-xl);
  font-weight: 600;
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: var(--space-sm);
}

.empty-requests {
  text-align: center;
  padding: var(--space-xl);
  color: var(--color-gray-medium);
  font-style: italic;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.request-card {
  background: var(--color-light);
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: var(--transition-normal);
}

.request-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.request-user {
  margin-bottom: var(--space-md);
}

.request-message {
  background: rgba(184, 134, 11, 0.1);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-style: italic;
  color: var(--color-dark);
  margin-bottom: var(--space-md);
  border-left: 4px solid var(--color-primary);
}

.request-actions {
  display: flex;
  gap: var(--space-sm);
}

.accept-btn,
.decline-btn,
.cancel-btn {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border: 2px solid;
}

.accept-btn {
  background: linear-gradient(135deg, var(--color-accent-green), var(--color-accent-emerald));
  color: var(--color-light);
  border-color: var(--color-accent-green);
}

.decline-btn,
.cancel-btn {
  background: var(--color-light);
  color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.accept-btn:hover,
.decline-btn:hover,
.cancel-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.decline-btn:hover,
.cancel-btn:hover {
  background: var(--color-secondary);
  color: var(--color-light);
}

/* User Search */
.user-search {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-header {
  margin-bottom: var(--space-lg);
}

.search-input-container {
  display: flex;
  gap: var(--space-sm);
}

.search-input {
  flex: 1;
  padding: var(--space-md);
  border: 2px solid rgba(184, 134, 11, 0.3);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: var(--transition-normal);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(184, 134, 11, 0.2);
}

.search-btn {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border: none;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  min-width: 80px;
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.search-results {
  flex: 1;
  overflow-y: auto;
}

.empty-results {
  text-align: center;
  padding: var(--space-2xl);
}

.empty-results .empty-icon {
  font-size: 3rem;
  margin-bottom: var(--space-lg);
  opacity: 0.5;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-lg);
}

.user-search-card {
  background: var(--color-light);
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: var(--transition-normal);
}

.user-search-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.search-user-info {
  margin-bottom: var(--space-md);
}

.user-bio {
  background: rgba(184, 134, 11, 0.1);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  color: var(--color-dark);
  margin-bottom: var(--space-md);
  border-left: 3px solid var(--color-primary);
}

.message-input-section {
  margin-bottom: var(--space-md);
}

.message-input {
  width: 100%;
  padding: var(--space-sm);
  border: 2px solid rgba(184, 134, 11, 0.3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  margin-bottom: var(--space-sm);
  transition: var(--transition-normal);
}

.message-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.message-actions {
  display: flex;
  gap: var(--space-sm);
}

.send-request-btn,
.cancel-message-btn {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border: 2px solid;
  font-size: var(--text-sm);
}

.send-request-btn {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border-color: var(--color-primary);
}

.cancel-message-btn {
  background: var(--color-light);
  color: var(--color-gray-medium);
  border-color: var(--color-gray-medium);
}

.friend-action-btn {
  width: 100%;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  border: 2px solid;
}

.friend-action-btn.primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border-color: var(--color-primary);
}

.friend-action-btn.disabled {
  background: var(--color-gray-light);
  color: var(--color-gray-medium);
  border-color: var(--color-gray-medium);
  cursor: not-allowed;
}

.friend-action-btn.primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Friend Suggestions Styles */
.friend-suggestions {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.suggestions-header {
  margin-bottom: var(--space-lg);
  text-align: center;
}

.suggestions-header h3 {
  color: var(--color-primary);
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-xl);
  font-weight: 600;
}

.suggestions-header p {
  color: var(--color-gray-medium);
  margin: 0;
  font-size: var(--text-sm);
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-lg);
  overflow-y: auto;
}

.suggestion-card {
  background: var(--color-light);
  border: 2px solid rgba(184, 134, 11, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: var(--transition-normal);
  position: relative;
}

.suggestion-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.suggestion-type {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  background: rgba(184, 134, 11, 0.1);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
}

.suggestion-icon {
  font-size: var(--text-sm);
}

.suggestion-label {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--color-primary);
}

.relevance-score {
  background: linear-gradient(135deg, var(--color-accent-green), var(--color-accent-emerald));
  color: var(--color-light);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 700;
  min-width: 40px;
  text-align: center;
}

.suggestion-user-info {
  margin-bottom: var(--space-md);
}

.suggestion-bio {
  background: rgba(184, 134, 11, 0.05);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  color: var(--color-dark);
  margin-bottom: var(--space-md);
  border-left: 3px solid var(--color-primary);
}

.suggestion-reasons {
  margin-bottom: var(--space-md);
}

.suggestion-reasons h5 {
  color: var(--color-primary);
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-sm);
  font-weight: 600;
}

.suggestion-reasons ul {
  margin: 0;
  padding-left: var(--space-md);
  list-style: none;
}

.suggestion-reasons li {
  font-size: var(--text-xs);
  color: var(--color-gray-medium);
  margin-bottom: var(--space-xs);
  position: relative;
}

.suggestion-reasons li::before {
  content: '✓';
  color: var(--color-accent-green);
  font-weight: bold;
  position: absolute;
  left: -var(--space-md);
}

.mutual-friends-btn {
  background: rgba(184, 134, 11, 0.1);
  border: 1px solid rgba(184, 134, 11, 0.3);
  color: var(--color-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  cursor: pointer;
  transition: var(--transition-normal);
  margin-bottom: var(--space-sm);
}

.mutual-friends-btn:hover {
  background: rgba(184, 134, 11, 0.2);
  border-color: var(--color-primary);
}

.mutual-friends-list {
  background: rgba(184, 134, 11, 0.05);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
  margin-bottom: var(--space-md);
  max-height: 120px;
  overflow-y: auto;
}

.mutual-friend-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.mutual-friend-item:hover {
  background: rgba(184, 134, 11, 0.1);
}

.mutual-friend-avatar {
  font-size: var(--text-sm);
}

.mutual-friend-name {
  font-size: var(--text-xs);
  color: var(--color-dark);
  font-weight: 500;
}

.no-mutual-friends {
  text-align: center;
  color: var(--color-gray-medium);
  font-size: var(--text-xs);
  font-style: italic;
}

.suggestion-message-input {
  margin-bottom: var(--space-md);
}

.suggestion-message-field {
  width: 100%;
  padding: var(--space-sm);
  border: 2px solid rgba(184, 134, 11, 0.3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  margin-bottom: var(--space-sm);
  transition: var(--transition-normal);
}

.suggestion-message-field:focus {
  outline: none;
  border-color: var(--color-primary);
}

.suggestion-message-actions {
  display: flex;
  gap: var(--space-sm);
}

.send-suggestion-request-btn,
.cancel-suggestion-btn {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  border: 2px solid;
  font-size: var(--text-sm);
}

.send-suggestion-request-btn {
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border-color: var(--color-primary);
  flex: 1;
}

.cancel-suggestion-btn {
  background: var(--color-light);
  color: var(--color-gray-medium);
  border-color: var(--color-gray-medium);
}

.suggestion-action-btn {
  width: 100%;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  border: 2px solid;
  background: linear-gradient(135deg, var(--color-primary), var(--color-heritage-gold));
  color: var(--color-light);
  border-color: var(--color-primary);
}

.suggestion-action-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.suggestion-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .friends-manager {
    padding: var(--space-sm);
  }

  .friends-content {
    max-height: 95vh;
  }

  .friends-header {
    padding: var(--space-md);
  }

  .friends-header h2 {
    font-size: var(--text-xl);
  }

  .friends-body {
    padding: var(--space-md);
  }

  .friends-grid,
  .users-grid,
  .suggestions-grid {
    grid-template-columns: 1fr;
  }

  .friend-actions,
  .request-actions,
  .message-actions,
  .suggestion-message-actions {
    flex-direction: column;
  }

  .search-input-container {
    flex-direction: column;
  }

  .tab-btn {
    padding: var(--space-sm);
    font-size: var(--text-sm);
  }

  .suggestion-card {
    padding: var(--space-md);
  }

  .suggestions-header h3 {
    font-size: var(--text-lg);
  }
}
